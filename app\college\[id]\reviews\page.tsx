"use client"

import { useState } from "react"
import {
  ArrowLeft,
  Star,
  ThumbsUp,
  ThumbsDown,
  TrendingUp,
  MessageSquare,
  Award,
  BookOpen,
  Home,
  Users,
  Utensils,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"

// Mock reviews data
const reviewsData = {
  overall: {
    rating: 4.6,
    totalReviews: 1247,
    distribution: {
      5: 65,
      4: 20,
      3: 10,
      2: 3,
      1: 2,
    },
  },
  categories: {
    academics: 4.7,
    infrastructure: 4.5,
    faculty: 4.6,
    placements: 4.8,
    hostel: 4.2,
    food: 3.9,
    campus: 4.6,
    social: 4.4,
  },
  reviews: [
    {
      id: 1,
      author: "Anonymous Student",
      batch: "2020-2024",
      department: "Computer Science",
      rating: 5,
      title: "Excellent academic environment and opportunities",
      content:
        "IIT Bombay has been an incredible journey. The faculty is world-class, research opportunities are abundant, and the peer learning environment is unmatched. The campus infrastructure is top-notch with excellent labs and facilities. Placement opportunities are outstanding with top companies visiting regularly.",
      likes: 45,
      dislikes: 2,
      helpful: 38,
      date: "2024-01-15",
      verified: true,
      categories: {
        academics: 5,
        infrastructure: 5,
        faculty: 5,
        placements: 5,
        hostel: 4,
        food: 3,
      },
      pros: ["World-class faculty", "Excellent research opportunities", "Top-tier placements", "Great peer network"],
      cons: ["Hostel food could be better", "High academic pressure"],
    },
    {
      id: 2,
      author: "Alumni",
      batch: "2018-2022",
      department: "Mechanical Engineering",
      rating: 4,
      title: "Great institute but intense academic pressure",
      content:
        "The academic rigor at IIT Bombay is intense but rewarding. Faculty members are highly knowledgeable and research-oriented. The campus life is vibrant with numerous clubs and activities. However, the competition can be overwhelming at times.",
      likes: 32,
      dislikes: 5,
      helpful: 28,
      date: "2024-01-10",
      verified: true,
      categories: {
        academics: 5,
        infrastructure: 4,
        faculty: 4,
        placements: 4,
        hostel: 4,
        food: 3,
      },
      pros: ["Strong academics", "Active campus life", "Good placement support"],
      cons: ["High stress levels", "Limited work-life balance"],
    },
    {
      id: 3,
      author: "Current Student",
      batch: "2021-2025",
      department: "Electronics Engineering",
      rating: 4,
      title: "Amazing opportunities but challenging environment",
      content:
        "The opportunities here are endless - from research projects to internships at top companies. The infrastructure is excellent and the library resources are vast. The only downside is the competitive atmosphere which can be stressful.",
      likes: 28,
      dislikes: 3,
      helpful: 22,
      date: "2024-01-08",
      verified: false,
      categories: {
        academics: 4,
        infrastructure: 5,
        faculty: 4,
        placements: 5,
        hostel: 4,
        food: 4,
      },
      pros: ["Excellent infrastructure", "Great internship opportunities", "Strong alumni network"],
      cons: ["Competitive environment", "Heavy workload"],
    },
  ],
}

export default function CollegeReviewsPage({ params }: { params: { id: string } }) {
  const [selectedFilter, setSelectedFilter] = useState("all")
  const [sortBy, setSortBy] = useState("helpful")
  const [showWriteReview, setShowWriteReview] = useState(false)
  const [newReview, setNewReview] = useState({
    rating: 0,
    title: "",
    content: "",
    categories: {
      academics: 0,
      infrastructure: 0,
      faculty: 0,
      placements: 0,
      hostel: 0,
      food: 0,
    },
  })

  const categoryIcons = {
    academics: BookOpen,
    infrastructure: Award,
    faculty: Users,
    placements: TrendingUp,
    hostel: Home,
    food: Utensils,
    campus: Award,
    social: Users,
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/college/iit-bombay">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to College
                </Button>
              </Link>
              <h1 className="text-2xl font-bold">IIT Bombay Reviews</h1>
            </div>
            <Button onClick={() => setShowWriteReview(true)}>Write Review</Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Rating Overview */}
          <div className="lg:col-span-1 space-y-6">
            {/* Overall Rating */}
            <Card>
              <CardHeader>
                <CardTitle>Overall Rating</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-5xl font-bold text-primary mb-2">{reviewsData.overall.rating}</div>
                  <div className="flex justify-center mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-6 w-6 ${
                          star <= reviewsData.overall.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <div className="text-muted-foreground">
                    {reviewsData.overall.totalReviews.toLocaleString()} reviews
                  </div>
                </div>

                {/* Rating Distribution */}
                <div className="space-y-2">
                  {[5, 4, 3, 2, 1].map((rating) => (
                    <div key={rating} className="flex items-center space-x-2">
                      <span className="text-sm w-3">{rating}</span>
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <Progress value={reviewsData.overall.distribution[rating]} className="flex-1" />
                      <span className="text-sm text-muted-foreground w-8">
                        {reviewsData.overall.distribution[rating]}%
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Category Ratings */}
            <Card>
              <CardHeader>
                <CardTitle>Category Ratings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(reviewsData.categories).map(([category, rating]) => {
                    const IconComponent = categoryIcons[category] || Award
                    return (
                      <div key={category} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-4 w-4 text-primary" />
                          <span className="text-sm capitalize">{category}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium">{rating}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Filters and Sort */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                    <SelectTrigger className="w-full sm:w-48">
                      <SelectValue placeholder="Filter by rating" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Reviews</SelectItem>
                      <SelectItem value="5">5 Stars</SelectItem>
                      <SelectItem value="4">4 Stars</SelectItem>
                      <SelectItem value="3">3 Stars</SelectItem>
                      <SelectItem value="2">2 Stars</SelectItem>
                      <SelectItem value="1">1 Star</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-full sm:w-48">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="helpful">Most Helpful</SelectItem>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="rating-high">Highest Rating</SelectItem>
                      <SelectItem value="rating-low">Lowest Rating</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Reviews List */}
            <div className="space-y-6">
              {reviewsData.reviews.map((review) => (
                <Card key={review.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarFallback>
                            {review.author
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="font-semibold">{review.author}</h4>
                            {review.verified && (
                              <Badge variant="secondary" className="text-xs">
                                Verified
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {review.department} • {review.batch}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1 mb-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-4 w-4 ${
                                star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(review.date).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <h3 className="font-semibold text-lg mb-3">{review.title}</h3>
                    <p className="text-muted-foreground mb-4 leading-relaxed">{review.content}</p>

                    {/* Category Ratings */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
                      {Object.entries(review.categories).map(([category, rating]) => {
                        const IconComponent = categoryIcons[category] || Award
                        return (
                          <div key={category} className="flex items-center space-x-2 text-sm">
                            <IconComponent className="h-3 w-3 text-primary" />
                            <span className="capitalize">{category}:</span>
                            <div className="flex">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                  key={star}
                                  className={`h-3 w-3 ${
                                    star <= rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    {/* Pros and Cons */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h5 className="font-medium text-green-700 mb-2">Pros</h5>
                        <ul className="space-y-1">
                          {review.pros.map((pro, index) => (
                            <li key={index} className="text-sm text-muted-foreground flex items-center space-x-2">
                              <div className="w-1 h-1 bg-green-500 rounded-full" />
                              <span>{pro}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h5 className="font-medium text-red-700 mb-2">Cons</h5>
                        <ul className="space-y-1">
                          {review.cons.map((con, index) => (
                            <li key={index} className="text-sm text-muted-foreground flex items-center space-x-2">
                              <div className="w-1 h-1 bg-red-500 rounded-full" />
                              <span>{con}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <Separator className="my-4" />

                    {/* Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-700">
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          {review.likes}
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                          <ThumbsDown className="h-4 w-4 mr-1" />
                          {review.dislikes}
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Reply
                        </Button>
                      </div>
                      <div className="text-sm text-muted-foreground">{review.helpful} people found this helpful</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center">
              <Button variant="outline">Load More Reviews</Button>
            </div>
          </div>
        </div>
      </div>

      {/* Write Review Modal */}
      {showWriteReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Write a Review</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setShowWriteReview(false)}>
                  ×
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Overall Rating */}
              <div>
                <Label className="text-base font-medium">Overall Rating</Label>
                <div className="flex items-center space-x-2 mt-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-8 w-8 cursor-pointer transition-colors ${
                        star <= newReview.rating
                          ? "fill-yellow-400 text-yellow-400"
                          : "text-gray-300 hover:text-yellow-200"
                      }`}
                      onClick={() => setNewReview({ ...newReview, rating: star })}
                    />
                  ))}
                </div>
              </div>

              {/* Category Ratings */}
              <div>
                <Label className="text-base font-medium">Category Ratings</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                  {Object.entries(newReview.categories).map(([category, rating]) => {
                    const IconComponent = categoryIcons[category] || Award
                    return (
                      <div key={category} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="h-4 w-4 text-primary" />
                          <span className="text-sm capitalize font-medium">{category}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-5 w-5 cursor-pointer transition-colors ${
                                star <= rating
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "text-gray-300 hover:text-yellow-200"
                              }`}
                              onClick={() =>
                                setNewReview({
                                  ...newReview,
                                  categories: { ...newReview.categories, [category]: star },
                                })
                              }
                            />
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Review Title */}
              <div>
                <Label htmlFor="title" className="text-base font-medium">
                  Review Title
                </Label>
                <Input
                  id="title"
                  placeholder="Summarize your experience..."
                  value={newReview.title}
                  onChange={(e) => setNewReview({ ...newReview, title: e.target.value })}
                  className="mt-2"
                />
              </div>

              {/* Review Content */}
              <div>
                <Label htmlFor="content" className="text-base font-medium">
                  Your Review
                </Label>
                <Textarea
                  id="content"
                  placeholder="Share your detailed experience about the college..."
                  value={newReview.content}
                  onChange={(e) => setNewReview({ ...newReview, content: e.target.value })}
                  className="mt-2 min-h-32"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={() => setShowWriteReview(false)}>
                  Cancel
                </Button>
                <Button>Submit Review</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
