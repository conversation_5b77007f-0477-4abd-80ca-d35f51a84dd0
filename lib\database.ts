import { supabase, College, Department, Faculty, Hostel, Fest, Club, Review, ForumPost } from './supabase'

// College-related functions
export async function getColleges(filters?: {
  counseling_category?: string
  type?: string
  state?: string
  search?: string
  limit?: number
  offset?: number
}) {
  let query = supabase
    .from('colleges')
    .select('*')
    .eq('is_active', true)

  if (filters?.counseling_category) {
    query = query.contains('counseling_categories', [filters.counseling_category])
  }

  if (filters?.type) {
    query = query.eq('type', filters.type)
  }

  if (filters?.state) {
    query = query.eq('state', filters.state)
  }

  if (filters?.search) {
    query = query.or(`name.ilike.%${filters.search}%,short_name.ilike.%${filters.search}%,location.ilike.%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
  }

  query = query.order('nirf_rank', { ascending: true, nullsLast: true })

  const { data, error } = await query

  if (error) {
    console.error('Error fetching colleges:', error)
    throw error
  }

  return data as College[]
}

export async function getCollegeBySlug(slug: string) {
  const { data, error } = await supabase
    .from('colleges')
    .select('*')
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) {
    console.error('Error fetching college:', error)
    throw error
  }

  return data as College
}

export async function getCollegeById(id: string) {
  const { data, error } = await supabase
    .from('colleges')
    .select('*')
    .eq('id', id)
    .eq('is_active', true)
    .single()

  if (error) {
    console.error('Error fetching college:', error)
    throw error
  }

  return data as College
}

// Department-related functions
export async function getDepartmentsByCollegeId(collegeId: string) {
  const { data, error } = await supabase
    .from('departments')
    .select('*')
    .eq('college_id', collegeId)
    .order('name')

  if (error) {
    console.error('Error fetching departments:', error)
    throw error
  }

  return data as Department[]
}

export async function getDepartmentById(id: string) {
  const { data, error } = await supabase
    .from('departments')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    console.error('Error fetching department:', error)
    throw error
  }

  return data as Department
}

// Faculty-related functions
export async function getFacultyByDepartmentId(departmentId: string) {
  const { data, error } = await supabase
    .from('faculty')
    .select('*')
    .eq('department_id', departmentId)
    .eq('is_active', true)
    .order('name')

  if (error) {
    console.error('Error fetching faculty:', error)
    throw error
  }

  return data as Faculty[]
}

// Hostel-related functions
export async function getHostelsByCollegeId(collegeId: string) {
  const { data, error } = await supabase
    .from('hostels')
    .select('*')
    .eq('college_id', collegeId)
    .order('name')

  if (error) {
    console.error('Error fetching hostels:', error)
    throw error
  }

  return data as Hostel[]
}

// Fest-related functions
export async function getFestsByCollegeId(collegeId: string) {
  const { data, error } = await supabase
    .from('fests')
    .select('*')
    .eq('college_id', collegeId)
    .order('name')

  if (error) {
    console.error('Error fetching fests:', error)
    throw error
  }

  return data as Fest[]
}

// Club-related functions
export async function getClubsByCollegeId(collegeId: string) {
  const { data, error } = await supabase
    .from('clubs')
    .select('*')
    .eq('college_id', collegeId)
    .order('name')

  if (error) {
    console.error('Error fetching clubs:', error)
    throw error
  }

  return data as Club[]
}

// Review-related functions
export async function getReviewsByEntity(entityType: string, entityId: string, limit = 10) {
  const { data, error } = await supabase
    .from('reviews')
    .select(`
      *,
      profiles:user_id (
        full_name,
        username,
        avatar_url,
        college_id
      )
    `)
    .eq('reviewable_type', entityType)
    .eq('reviewable_id', entityId)
    .eq('status', 'approved')
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching reviews:', error)
    throw error
  }

  return data
}

export async function createReview(review: Omit<Review, 'id' | 'created_at' | 'updated_at' | 'helpful_count' | 'not_helpful_count' | 'status'>) {
  const { data, error } = await supabase
    .from('reviews')
    .insert([review])
    .select()
    .single()

  if (error) {
    console.error('Error creating review:', error)
    throw error
  }

  return data as Review
}

// Forum-related functions
export async function getForumPosts(filters?: {
  category?: string
  college_id?: string
  search?: string
  limit?: number
  offset?: number
}) {
  let query = supabase
    .from('forum_posts')
    .select(`
      *,
      profiles:user_id (
        full_name,
        username,
        avatar_url,
        college_id
      ),
      colleges:college_id (
        name,
        short_name
      )
    `)
    .eq('status', 'approved')

  if (filters?.category) {
    query = query.eq('category', filters.category)
  }

  if (filters?.college_id) {
    query = query.eq('college_id', filters.college_id)
  }

  if (filters?.search) {
    query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
  }

  query = query.order('is_pinned', { ascending: false })
    .order('is_trending', { ascending: false })
    .order('created_at', { ascending: false })

  const { data, error } = await query

  if (error) {
    console.error('Error fetching forum posts:', error)
    throw error
  }

  return data
}

export async function createForumPost(post: Omit<ForumPost, 'id' | 'created_at' | 'updated_at' | 'upvotes' | 'downvotes' | 'total_replies' | 'is_trending' | 'is_pinned' | 'status'>) {
  const { data, error } = await supabase
    .from('forum_posts')
    .insert([post])
    .select()
    .single()

  if (error) {
    console.error('Error creating forum post:', error)
    throw error
  }

  return data as ForumPost
}

// Search function
export async function searchContent(query: string, type?: 'colleges' | 'posts') {
  const results: any = {}

  if (!type || type === 'colleges') {
    const { data: colleges } = await supabase
      .from('colleges')
      .select('id, name, short_name, slug, location, logo_url, rating, nirf_rank')
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,short_name.ilike.%${query}%,location.ilike.%${query}%`)
      .limit(5)

    results.colleges = colleges || []
  }

  if (!type || type === 'posts') {
    const { data: posts } = await supabase
      .from('forum_posts')
      .select('id, title, category, created_at')
      .eq('status', 'approved')
      .or(`title.ilike.%${query}%,content.ilike.%${query}%`)
      .limit(5)

    results.posts = posts || []
  }

  return results
}
