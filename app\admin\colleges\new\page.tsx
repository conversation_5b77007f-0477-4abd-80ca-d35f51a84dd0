"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import {
  Building2,
  Save,
  ArrowLeft,
  Upload,
  X,
  Plus,
  MapPin,
  Globe,
  Phone,
  Mail,
  Calendar,
  Users,
  Star,
  Trophy,
  IndianRupee,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { AdminLayout } from "@/components/admin/admin-layout"
import { withAdminAuth } from "@/lib/auth"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

interface CollegeFormData {
  name: string
  short_name: string
  description: string
  type: "government" | "private" | "deemed" | ""
  counseling_categories: string[]
  established: string
  location: string
  state: string
  city: string
  pincode: string
  website: string
  phone: string
  email: string
  nirf_rank: string
  annual_fees: string
  placement_rate: string
  average_package: string
  highest_package: string
  highlights: string[]
}

const initialFormData: CollegeFormData = {
  name: "",
  short_name: "",
  description: "",
  type: "",
  counseling_categories: [],
  established: "",
  location: "",
  state: "",
  city: "",
  pincode: "",
  website: "",
  phone: "",
  email: "",
  nirf_rank: "",
  annual_fees: "",
  placement_rate: "",
  average_package: "",
  highest_package: "",
  highlights: [],
}

const counselingCategories = [
  { id: "josaa-csab", label: "JOSAA/CSAB" },
  { id: "jac-delhi", label: "JAC Delhi" },
  { id: "comedk-kcet", label: "COMEDK/KCET" },
  { id: "mht-cet", label: "MHT-CET" },
  { id: "wbjee", label: "WBJEE" },
  { id: "tnea", label: "TNEA" },
]

const indianStates = [
  "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh",
  "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka",
  "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram",
  "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu",
  "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal",
  "Delhi", "Jammu and Kashmir", "Ladakh", "Puducherry"
]

function AddCollegePage() {
  const [formData, setFormData] = useState<CollegeFormData>(initialFormData)
  const [newHighlight, setNewHighlight] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleInputChange = (field: keyof CollegeFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      counseling_categories: checked
        ? [...prev.counseling_categories, categoryId]
        : prev.counseling_categories.filter(id => id !== categoryId)
    }))
  }

  const addHighlight = () => {
    if (newHighlight.trim() && !formData.highlights.includes(newHighlight.trim())) {
      setFormData(prev => ({
        ...prev,
        highlights: [...prev.highlights, newHighlight.trim()]
      }))
      setNewHighlight("")
    }
  }

  const removeHighlight = (index: number) => {
    setFormData(prev => ({
      ...prev,
      highlights: prev.highlights.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Here you would normally submit to your API
      // const response = await createCollege(formData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      toast({
        title: "College Added Successfully",
        description: `${formData.name} has been added to the database.`,
      })

      router.push("/admin/colleges")
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add college. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/colleges">
              <Button variant="ghost" className="hover:bg-primary/10">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Colleges
              </Button>
            </Link>
            <div>
              <h1 className="text-4xl font-bold font-space-grotesk text-foreground">
                Add New College
              </h1>
              <p className="text-muted-foreground font-dm-sans mt-2">
                Add a new college to the CollegeHub database
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <Card className="border-2 border-border">
            <CardHeader>
              <CardTitle className="text-2xl font-space-grotesk flex items-center">
                <Building2 className="h-6 w-6 mr-2 text-primary" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="name" className="text-sm font-semibold text-foreground">
                    College Name *
                  </Label>
                  <Input
                    id="name"
                    placeholder="Indian Institute of Technology Bombay"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    required
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="short_name" className="text-sm font-semibold text-foreground">
                    Short Name
                  </Label>
                  <Input
                    id="short_name"
                    placeholder="IIT Bombay"
                    value={formData.short_name}
                    onChange={(e) => handleInputChange("short_name", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description" className="text-sm font-semibold text-foreground">
                  Description *
                </Label>
                <Textarea
                  id="description"
                  placeholder="Brief description of the college..."
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  required
                  rows={4}
                  className="mt-2 border-2 border-border focus:border-primary resize-none"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="type" className="text-sm font-semibold text-foreground">
                    College Type *
                  </Label>
                  <Select value={formData.type} onValueChange={(value) => handleInputChange("type", value)}>
                    <SelectTrigger className="mt-2 border-2 border-border">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="government">Government</SelectItem>
                      <SelectItem value="private">Private</SelectItem>
                      <SelectItem value="deemed">Deemed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="established" className="text-sm font-semibold text-foreground">
                    Established Year
                  </Label>
                  <Input
                    id="established"
                    type="number"
                    placeholder="1958"
                    value={formData.established}
                    onChange={(e) => handleInputChange("established", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="nirf_rank" className="text-sm font-semibold text-foreground">
                    NIRF Rank
                  </Label>
                  <Input
                    id="nirf_rank"
                    type="number"
                    placeholder="3"
                    value={formData.nirf_rank}
                    onChange={(e) => handleInputChange("nirf_rank", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Information */}
          <Card className="border-2 border-border">
            <CardHeader>
              <CardTitle className="text-2xl font-space-grotesk flex items-center">
                <MapPin className="h-6 w-6 mr-2 text-secondary" />
                Location Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="location" className="text-sm font-semibold text-foreground">
                  Full Address *
                </Label>
                <Input
                  id="location"
                  placeholder="Powai, Mumbai, Maharashtra"
                  value={formData.location}
                  onChange={(e) => handleInputChange("location", e.target.value)}
                  required
                  className="mt-2 border-2 border-border focus:border-primary"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="state" className="text-sm font-semibold text-foreground">
                    State *
                  </Label>
                  <Select value={formData.state} onValueChange={(value) => handleInputChange("state", value)}>
                    <SelectTrigger className="mt-2 border-2 border-border">
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>
                      {indianStates.map((state) => (
                        <SelectItem key={state} value={state.toLowerCase().replace(/\s+/g, '-')}>
                          {state}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="city" className="text-sm font-semibold text-foreground">
                    City *
                  </Label>
                  <Input
                    id="city"
                    placeholder="Mumbai"
                    value={formData.city}
                    onChange={(e) => handleInputChange("city", e.target.value)}
                    required
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="pincode" className="text-sm font-semibold text-foreground">
                    Pincode
                  </Label>
                  <Input
                    id="pincode"
                    placeholder="400076"
                    value={formData.pincode}
                    onChange={(e) => handleInputChange("pincode", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card className="border-2 border-border">
            <CardHeader>
              <CardTitle className="text-2xl font-space-grotesk flex items-center">
                <Phone className="h-6 w-6 mr-2 text-green-600" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="website" className="text-sm font-semibold text-foreground">
                    Website
                  </Label>
                  <Input
                    id="website"
                    type="url"
                    placeholder="https://www.iitb.ac.in"
                    value={formData.website}
                    onChange={(e) => handleInputChange("website", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="phone" className="text-sm font-semibold text-foreground">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    placeholder="+91-22-2572-2545"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-sm font-semibold text-foreground">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="mt-2 border-2 border-border focus:border-primary"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Counseling Categories */}
          <Card className="border-2 border-border">
            <CardHeader>
              <CardTitle className="text-2xl font-space-grotesk">
                Counseling Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {counselingCategories.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={category.id}
                      checked={formData.counseling_categories.includes(category.id)}
                      onCheckedChange={(checked) => handleCategoryChange(category.id, checked as boolean)}
                    />
                    <Label htmlFor={category.id} className="text-sm font-dm-sans">
                      {category.label}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex items-center justify-end space-x-4">
            <Link href="/admin/colleges">
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary hover:bg-primary/90 px-8"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
                  Adding College...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Add College
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </AdminLayout>
  )
}

// Temporarily disabled auth for development
export default AddCollegePage
// export default withAdminAuth(AddCollegePage)
