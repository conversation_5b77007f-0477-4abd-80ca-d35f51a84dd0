import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface College {
  id: string
  name: string
  short_name?: string
  slug: string
  description?: string
  type: 'government' | 'private' | 'deemed'
  counseling_categories: string[]
  established?: number
  location: string
  state: string
  city: string
  pincode?: string
  website?: string
  phone?: string
  email?: string
  logo_url?: string
  banner_url?: string
  nirf_rank?: number
  rating: number
  total_reviews: number
  total_students?: number
  total_faculty?: number
  total_departments?: number
  placement_rate?: number
  average_package?: string
  highest_package?: string
  annual_fees?: string
  highlights?: string[]
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Department {
  id: string
  college_id: string
  name: string
  short_name?: string
  description?: string
  total_students: number
  total_faculty: number
  rating: number
  total_reviews: number
  labs?: string[]
  collaborations?: string[]
  accreditations?: string[]
  created_at: string
  updated_at: string
}

export interface Faculty {
  id: string
  department_id: string
  name: string
  designation?: string
  qualification?: string
  specialization?: string[]
  research_areas?: string[]
  experience_years?: number
  email?: string
  phone?: string
  image_url?: string
  rating: number
  total_reviews: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Hostel {
  id: string
  college_id: string
  name: string
  type?: string
  occupancy_type?: string
  annual_fees?: string
  amenities?: string[]
  rules?: string
  mess_timings?: any
  nearby_facilities?: string[]
  images?: string[]
  rating: number
  total_reviews: number
  created_at: string
  updated_at: string
}

export interface Fest {
  id: string
  college_id: string
  name: string
  type?: string
  description?: string
  duration?: string
  budget?: string
  events?: string[]
  images?: string[]
  website?: string
  social_links?: any
  created_at: string
  updated_at: string
}

export interface Club {
  id: string
  college_id: string
  name: string
  category?: string
  description?: string
  total_members: number
  achievements?: string[]
  contact_email?: string
  contact_phone?: string
  social_links?: any
  created_at: string
  updated_at: string
}

export interface Profile {
  id: string
  email: string
  full_name?: string
  username?: string
  role: 'student' | 'moderator' | 'admin'
  college_id?: string
  graduation_year?: number
  branch?: string
  bio?: string
  avatar_url?: string
  is_verified: boolean
  created_at: string
  updated_at: string
}

export interface Review {
  id: string
  user_id: string
  reviewable_type: string
  reviewable_id: string
  rating: number
  title?: string
  content: string
  pros?: string[]
  cons?: string[]
  tags?: string[]
  helpful_count: number
  not_helpful_count: number
  status: 'pending' | 'approved' | 'rejected' | 'flagged'
  is_anonymous: boolean
  created_at: string
  updated_at: string
}

export interface ForumPost {
  id: string
  user_id: string
  title: string
  content: string
  category: 'college_comparison' | 'placements' | 'campus_life' | 'exam_preparation' | 'admissions' | 'faculty_reviews'
  tags?: string[]
  college_id?: string
  upvotes: number
  downvotes: number
  total_replies: number
  is_trending: boolean
  is_pinned: boolean
  status: 'pending' | 'approved' | 'rejected' | 'flagged'
  is_anonymous: boolean
  created_at: string
  updated_at: string
}
