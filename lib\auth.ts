"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { User } from "@supabase/supabase-js"
// import { supabase, Profile } from "@/lib/supabase"
import { useRouter } from "next/navigation"

// Temporary Profile interface for development
interface Profile {
  id: string
  email: string
  full_name?: string
  username?: string
  role: 'student' | 'moderator' | 'admin'
  college_id?: string
  graduation_year?: number
  branch?: string
  bio?: string
  avatar_url?: string
  is_verified: boolean
  created_at: string
  updated_at: string
}

interface AuthContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  signOut: () => Promise<void>
  isAdmin: boolean
  isModerator: boolean
  hasAdminAccess: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    // Temporary mock auth for development
    setLoading(false)
    // TODO: Implement real auth when Supabase is properly configured
  }, [])

  const fetchProfile = async (userId: string) => {
    // TODO: Implement real profile fetching
    console.log('Fetching profile for:', userId)
  }

  const signOut = async () => {
    try {
      // TODO: Implement real sign out
      setUser(null)
      setProfile(null)
      router.push('/admin/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const isAdmin = profile?.role === 'admin'
  const isModerator = profile?.role === 'moderator'
  const hasAdminAccess = isAdmin || isModerator

  const value = {
    user,
    profile,
    loading,
    signOut,
    isAdmin,
    isModerator,
    hasAdminAccess,
  }

  return React.createElement(AuthContext.Provider, { value }, children)
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Higher-order component for protecting admin routes
export function withAdminAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  return function AdminProtectedComponent(props: P) {
    const { hasAdminAccess, loading } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (!loading && !hasAdminAccess) {
        router.push('/admin/login')
      }
    }, [hasAdminAccess, loading, router])

    if (loading) {
      return React.createElement('div',
        { className: "min-h-screen flex items-center justify-center" },
        React.createElement('div', { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary" })
      )
    }

    if (!hasAdminAccess) {
      return null
    }

    return React.createElement(WrappedComponent, props)
  }
}

// Hook for checking specific permissions
export function usePermissions() {
  const { profile, isAdmin, isModerator, hasAdminAccess } = useAuth()

  const canManageColleges = isAdmin || isModerator
  const canManageFaculty = isAdmin || isModerator
  const canManageReviews = isAdmin || isModerator
  const canManageUsers = isAdmin
  const canManageSettings = isAdmin
  const canViewAnalytics = isAdmin || isModerator
  const canModerateContent = isAdmin || isModerator

  return {
    canManageColleges,
    canManageFaculty,
    canManageReviews,
    canManageUsers,
    canManageSettings,
    canViewAnalytics,
    canModerateContent,
    isAdmin,
    isModerator,
    hasAdminAccess,
    role: profile?.role,
  }
}

// Utility function to check if user has required role
export function hasRole(userRole: string | undefined, requiredRoles: string[]): boolean {
  if (!userRole) return false
  return requiredRoles.includes(userRole)
}

// Utility function to redirect unauthorized users
export function redirectUnauthorized() {
  if (typeof window !== 'undefined') {
    window.location.href = '/admin/login'
  }
}
