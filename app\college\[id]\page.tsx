"use client"

import React from "react"
import {
  ArrowLeft,
  MapPin,
  GraduationCap,
  Phone,
  Mail,
  Globe,
  Users,
  Share2,
  Heart,
  ExternalLink,
  IndianRupee,
  Building2,
  BookOpen,
  Award,
  Wifi,
  Car,
  Utensils,
  Dumbbell,
  Library,
  Microscope,
  Calendar,
  Clock,
  Star,
  TrendingUp,
  Home,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/header"
import { RatingStars } from "@/components/rating-stars"
import Link from "next/link"
import Image from "next/image"
import { College } from "@/lib/supabase"

interface CollegeDetailPageProps {
  params: Promise<{ id: string }>
}

// Mock data for development - will be replaced with real data
const mockCollege: College = {
  id: "1",
  name: "Indian Institute of Technology Bombay",
  short_name: "IIT Bombay",
  slug: "iit-bombay",
  description: "Premier engineering and technology institute known for excellence in education and research with world-class faculty and cutting-edge facilities.",
  type: "government",
  counseling_categories: ["josaa-csab"],
  established: 1958,
  location: "Powai, Mumbai, Maharashtra",
  state: "Maharashtra",
  city: "Mumbai",
  pincode: "400076",
  website: "https://www.iitb.ac.in",
  phone: "+91-22-2572-2545",
  email: "<EMAIL>",
  logo_url: "/iit-bombay-logo.png",
  banner_url: "/iit-bombay-aerial.png",
  nirf_rank: 3,
  rating: 4.6,
  total_reviews: 1247,
  total_students: 11000,
  total_faculty: 650,
  total_departments: 18,
  placement_rate: 95,
  average_package: "₹18.5 LPA",
  highest_package: "₹2.14 Crore",
  annual_fees: "₹2.5 Lakhs",
  highlights: ["Top 3 NIRF Ranking", "95% Placement Rate", "650+ Faculty", "World-class Research"],
  is_active: true,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
}

// Mock fee structure data
const mockFeeStructure = {
  tuition_fees: "₹2,50,000",
  hostel_fees: "₹15,000",
  mess_fees: "₹45,000",
  other_fees: "₹10,000",
  total_annual_fees: "₹3,20,000",
  fee_breakdown: [
    { category: "Tuition Fees", amount: "₹2,50,000", description: "Academic fees for the year" },
    { category: "Hostel Fees", amount: "₹15,000", description: "Accommodation charges" },
    { category: "Mess Fees", amount: "₹45,000", description: "Food and dining charges" },
    { category: "Development Fee", amount: "₹5,000", description: "Infrastructure development" },
    { category: "Other Charges", amount: "₹5,000", description: "Library, sports, and misc fees" },
  ]
}

// Mock hostel information
const mockHostelInfo = {
  total_hostels: 13,
  total_capacity: 8500,
  availability: "Available",
  hostel_types: ["Single Occupancy", "Double Occupancy", "Triple Occupancy"],
  amenities: ["Wi-Fi", "Laundry", "Common Room", "Study Hall", "Gym", "Mess", "Medical Facility"],
  rules: "Strict discipline maintained. No outside food allowed. Visitors allowed till 9 PM.",
  mess_timings: {
    breakfast: "7:30 AM - 9:30 AM",
    lunch: "12:00 PM - 2:00 PM",
    snacks: "4:30 PM - 6:00 PM",
    dinner: "7:30 PM - 9:30 PM"
  }
}

// Mock departments data
const mockDepartments = [
  { id: "1", name: "Computer Science & Engineering", students: 800, faculty: 45, rating: 4.7 },
  { id: "2", name: "Electrical Engineering", students: 600, faculty: 38, rating: 4.6 },
  { id: "3", name: "Mechanical Engineering", students: 700, faculty: 42, rating: 4.5 },
  { id: "4", name: "Chemical Engineering", students: 400, faculty: 28, rating: 4.6 },
  { id: "5", name: "Civil Engineering", students: 500, faculty: 32, rating: 4.4 },
  { id: "6", name: "Aerospace Engineering", students: 300, faculty: 22, rating: 4.8 },
]

// Mock infrastructure data
const mockInfrastructure = {
  labs: [
    { name: "Advanced Computing Lab", capacity: 100, equipment: "High-end workstations, servers" },
    { name: "Electronics Lab", capacity: 60, equipment: "Oscilloscopes, signal generators" },
    { name: "Mechanical Workshop", capacity: 80, equipment: "CNC machines, 3D printers" },
    { name: "Chemical Process Lab", capacity: 40, equipment: "Reactors, distillation units" },
  ],
  libraries: [
    { name: "Central Library", books: 500000, digital_resources: "IEEE, ACM, Springer" },
    { name: "Departmental Libraries", books: 150000, digital_resources: "Subject-specific journals" },
  ],
  sports_facilities: [
    "Olympic-size Swimming Pool", "Cricket Ground", "Football Field", "Basketball Courts",
    "Tennis Courts", "Badminton Courts", "Gymnasium", "Athletics Track"
  ],
  other_facilities: [
    "Auditorium (2000 capacity)", "Convention Center", "Guest House", "Medical Center",
    "Bank & ATM", "Post Office", "Shopping Complex", "Food Courts"
  ]
}

export default function CollegeDetailPage({ params }: CollegeDetailPageProps) {
  const [resolvedParams, setResolvedParams] = React.useState<{ id: string } | null>(null)

  // Resolve params Promise
  React.useEffect(() => {
    params.then(setResolvedParams)
  }, [params])

  if (!resolvedParams) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  // For development, using mock data. In production, this would fetch real data
  const college = mockCollege

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-to-br from-primary/5 via-secondary/5 to-background overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          {/* Back Button */}
          <div className="mb-8">
            <Link href="/counseling/josaa-csab">
              <Button variant="ghost" className="hover:bg-primary/10">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Results
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* College Info */}
            <div className="lg:col-span-2">
              <div className="flex items-start space-x-6 mb-6">
                {/* Logo */}
                <div className="relative w-24 h-24 flex-shrink-0">
                  {college.logo_url ? (
                    <Image
                      src={college.logo_url}
                      alt={`${college.name} logo`}
                      fill
                      className="object-contain rounded-xl border-2 border-border bg-card"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                      <GraduationCap className="h-12 w-12 text-primary-foreground" />
                    </div>
                  )}
                </div>

                {/* Basic Info */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h1 className="text-4xl font-bold font-space-grotesk text-foreground mb-2">
                        {college.name}
                      </h1>
                      {college.short_name && college.short_name !== college.name && (
                        <p className="text-xl text-muted-foreground font-dm-sans mb-3">
                          {college.short_name}
                        </p>
                      )}
                      <div className="flex items-center space-x-2 mb-3">
                        <MapPin className="h-5 w-5 text-muted-foreground" />
                        <span className="text-lg text-muted-foreground font-dm-sans">{college.location}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="icon">
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="icon">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center space-x-4">
                    <RatingStars rating={college.rating} readonly showValue size="lg" />
                    <span className="text-muted-foreground font-dm-sans">
                      ({college.total_reviews.toLocaleString()} reviews)
                    </span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-lg text-muted-foreground font-dm-sans leading-relaxed mb-6">
                {college.description}
              </p>
            </div>

            {/* Quick Stats */}
            <div className="lg:col-span-1">
              <Card className="border-2 border-border">
                <CardHeader>
                  <CardTitle className="text-xl font-space-grotesk">Quick Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold font-space-grotesk text-primary">
                        {college.total_students ? `${Math.round(college.total_students / 1000)}K` : "N/A"}
                      </div>
                      <p className="text-sm text-muted-foreground font-dm-sans">Students</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold font-space-grotesk text-secondary">
                        {college.total_faculty || "N/A"}
                      </div>
                      <p className="text-sm text-muted-foreground font-dm-sans">Faculty</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Fee Structure Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-space-grotesk text-foreground mb-4">
              Fee Structure
            </h2>
            <p className="text-lg text-muted-foreground font-dm-sans max-w-2xl mx-auto">
              Transparent and affordable fee structure with detailed breakdown
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Fee Summary */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <IndianRupee className="h-5 w-5 text-primary" />
                  <span>Annual Fee Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-border">
                  <span className="font-dm-sans">Tuition Fees</span>
                  <span className="font-semibold">{mockFeeStructure.tuition_fees}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-border">
                  <span className="font-dm-sans">Hostel Fees</span>
                  <span className="font-semibold">{mockFeeStructure.hostel_fees}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-border">
                  <span className="font-dm-sans">Mess Fees</span>
                  <span className="font-semibold">{mockFeeStructure.mess_fees}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-border">
                  <span className="font-dm-sans">Other Fees</span>
                  <span className="font-semibold">{mockFeeStructure.other_fees}</span>
                </div>
                <div className="flex justify-between items-center py-3 bg-primary/10 px-4 rounded-lg">
                  <span className="font-semibold font-dm-sans">Total Annual Fees</span>
                  <span className="font-bold text-lg text-primary">{mockFeeStructure.total_annual_fees}</span>
                </div>
              </CardContent>
            </Card>

            {/* Fee Breakdown */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle>Detailed Breakdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {mockFeeStructure.fee_breakdown.map((fee, index) => (
                  <div key={index} className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold font-dm-sans">{fee.category}</h4>
                      <span className="font-bold text-primary">{fee.amount}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{fee.description}</p>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Hostel Information Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-space-grotesk text-foreground mb-4">
              Hostel Information
            </h2>
            <p className="text-lg text-muted-foreground font-dm-sans max-w-2xl mx-auto">
              Comfortable and secure accommodation with modern amenities
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Hostel Overview */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Home className="h-5 w-5 text-primary" />
                  <span>Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold font-space-grotesk text-primary">
                      {mockHostelInfo.total_hostels}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Hostels</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold font-space-grotesk text-secondary">
                      {mockHostelInfo.total_capacity.toLocaleString()}
                    </div>
                    <p className="text-sm text-muted-foreground">Capacity</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-dm-sans">Availability</span>
                    <Badge className="bg-green-100 text-green-700 border-green-200">
                      {mockHostelInfo.availability}
                    </Badge>
                  </div>
                  <div>
                    <span className="font-dm-sans font-semibold">Room Types:</span>
                    <div className="mt-2 space-y-1">
                      {mockHostelInfo.hostel_types.map((type, index) => (
                        <Badge key={index} variant="outline" className="mr-2">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Amenities */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Wifi className="h-5 w-5 text-primary" />
                  <span>Amenities</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {mockHostelInfo.amenities.map((amenity, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm font-dm-sans">{amenity}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Mess Timings */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Utensils className="h-5 w-5 text-primary" />
                  <span>Mess Timings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(mockHostelInfo.mess_timings).map(([meal, timing]) => (
                  <div key={meal} className="flex justify-between items-center">
                    <span className="font-dm-sans capitalize">{meal}</span>
                    <span className="text-sm text-muted-foreground">{timing}</span>
                  </div>
                ))}
                <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                  <p className="text-sm text-muted-foreground">{mockHostelInfo.rules}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Departments Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-space-grotesk text-foreground mb-4">
              Departments
            </h2>
            <p className="text-lg text-muted-foreground font-dm-sans max-w-2xl mx-auto">
              Explore our diverse range of academic departments and programs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockDepartments.map((dept) => (
              <Card key={dept.id} className="border-2 border-border hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <GraduationCap className="h-5 w-5 text-primary" />
                    <span className="text-lg">{dept.name}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-xl font-bold font-space-grotesk text-primary">
                        {dept.students}
                      </div>
                      <p className="text-sm text-muted-foreground">Students</p>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold font-space-grotesk text-secondary">
                        {dept.faculty}
                      </div>
                      <p className="text-sm text-muted-foreground">Faculty</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="font-semibold">{dept.rating}</span>
                    </div>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Infrastructure Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-space-grotesk text-foreground mb-4">
              Infrastructure & Facilities
            </h2>
            <p className="text-lg text-muted-foreground font-dm-sans max-w-2xl mx-auto">
              State-of-the-art facilities to support learning, research, and recreation
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Labs */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Microscope className="h-5 w-5 text-primary" />
                  <span>Laboratories</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {mockInfrastructure.labs.map((lab, index) => (
                  <div key={index} className="p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold font-dm-sans mb-2">{lab.name}</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Capacity: </span>
                        <span className="font-semibold">{lab.capacity}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Equipment: </span>
                        <span className="font-semibold">{lab.equipment}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Libraries */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Library className="h-5 w-5 text-primary" />
                  <span>Libraries</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {mockInfrastructure.libraries.map((library, index) => (
                  <div key={index} className="p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold font-dm-sans mb-2">{library.name}</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Books: </span>
                        <span className="font-semibold">{library.books.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Digital Resources: </span>
                        <span className="font-semibold">{library.digital_resources}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Sports Facilities */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Dumbbell className="h-5 w-5 text-primary" />
                  <span>Sports Facilities</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  {mockInfrastructure.sports_facilities.map((facility, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm font-dm-sans">{facility}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Other Facilities */}
            <Card className="border-2 border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Building2 className="h-5 w-5 text-primary" />
                  <span>Other Facilities</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-3">
                  {mockInfrastructure.other_facilities.map((facility, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-secondary rounded-full"></div>
                      <span className="text-sm font-dm-sans">{facility}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Information Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold font-space-grotesk text-foreground mb-4">
              Contact Information
            </h2>
            <p className="text-lg text-muted-foreground font-dm-sans max-w-2xl mx-auto">
              Get in touch with the college for admissions and inquiries
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="border-2 border-border text-center">
              <CardContent className="pt-6">
                <Phone className="h-8 w-8 text-primary mx-auto mb-4" />
                <h3 className="font-semibold font-space-grotesk mb-2">Phone</h3>
                <p className="text-muted-foreground font-dm-sans">{college.phone}</p>
              </CardContent>
            </Card>

            <Card className="border-2 border-border text-center">
              <CardContent className="pt-6">
                <Mail className="h-8 w-8 text-primary mx-auto mb-4" />
                <h3 className="font-semibold font-space-grotesk mb-2">Email</h3>
                <p className="text-muted-foreground font-dm-sans">{college.email}</p>
              </CardContent>
            </Card>

            <Card className="border-2 border-border text-center">
              <CardContent className="pt-6">
                <Globe className="h-8 w-8 text-primary mx-auto mb-4" />
                <h3 className="font-semibold font-space-grotesk mb-2">Website</h3>
                <a
                  href={college.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline font-dm-sans"
                >
                  Visit Website
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}