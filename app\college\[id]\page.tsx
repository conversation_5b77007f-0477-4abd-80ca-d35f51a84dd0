"use client"

import React from "react"
import {
  ArrowLeft,
  MapPin,
  GraduationCap,
  Phone,
  Mail,
  Globe,
  Users,
  Share2,
  Heart,
  ExternalLink,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/header"
import { RatingStars } from "@/components/rating-stars"
import Link from "next/link"
import Image from "next/image"
import { College } from "@/lib/supabase"

interface CollegeDetailPageProps {
  params: Promise<{ id: string }>
}

// Mock data for development - will be replaced with real data
const mockCollege: College = {
  id: "1",
  name: "Indian Institute of Technology Bombay",
  short_name: "IIT Bombay",
  slug: "iit-bombay",
  description: "Premier engineering and technology institute known for excellence in education and research with world-class faculty and cutting-edge facilities.",
  type: "government",
  counseling_categories: ["josaa-csab"],
  established: 1958,
  location: "Powai, Mumbai, Maharashtra",
  state: "Maharashtra",
  city: "Mumbai",
  pincode: "400076",
  website: "https://www.iitb.ac.in",
  phone: "+91-22-2572-2545",
  email: "<EMAIL>",
  logo_url: "/iit-bombay-logo.png",
  banner_url: "/iit-bombay-aerial.png",
  nirf_rank: 3,
  rating: 4.6,
  total_reviews: 1247,
  total_students: 11000,
  total_faculty: 650,
  total_departments: 18,
  placement_rate: 95,
  average_package: "₹18.5 LPA",
  highest_package: "₹2.14 Crore",
  annual_fees: "₹2.5 Lakhs",
  highlights: ["Top 3 NIRF Ranking", "95% Placement Rate", "650+ Faculty", "World-class Research"],
  is_active: true,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
}

export default function CollegeDetailPage({ params }: CollegeDetailPageProps) {
  const [resolvedParams, setResolvedParams] = React.useState<{ id: string } | null>(null)

  // Resolve params Promise
  React.useEffect(() => {
    params.then(setResolvedParams)
  }, [params])

  if (!resolvedParams) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  // For development, using mock data. In production, this would fetch real data
  const college = mockCollege

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <section className="relative py-16 bg-gradient-to-br from-primary/5 via-secondary/5 to-background overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          {/* Back Button */}
          <div className="mb-8">
            <Link href="/counseling/josaa-csab">
              <Button variant="ghost" className="hover:bg-primary/10">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Results
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* College Info */}
            <div className="lg:col-span-2">
              <div className="flex items-start space-x-6 mb-6">
                {/* Logo */}
                <div className="relative w-24 h-24 flex-shrink-0">
                  {college.logo_url ? (
                    <Image
                      src={college.logo_url}
                      alt={`${college.name} logo`}
                      fill
                      className="object-contain rounded-xl border-2 border-border bg-card"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                      <GraduationCap className="h-12 w-12 text-primary-foreground" />
                    </div>
                  )}
                </div>

                {/* Basic Info */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h1 className="text-4xl font-bold font-space-grotesk text-foreground mb-2">
                        {college.name}
                      </h1>
                      {college.short_name && college.short_name !== college.name && (
                        <p className="text-xl text-muted-foreground font-dm-sans mb-3">
                          {college.short_name}
                        </p>
                      )}
                      <div className="flex items-center space-x-2 mb-3">
                        <MapPin className="h-5 w-5 text-muted-foreground" />
                        <span className="text-lg text-muted-foreground font-dm-sans">{college.location}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="icon">
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="icon">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="flex items-center space-x-4">
                    <RatingStars rating={college.rating} readonly showValue size="lg" />
                    <span className="text-muted-foreground font-dm-sans">
                      ({college.total_reviews.toLocaleString()} reviews)
                    </span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-lg text-muted-foreground font-dm-sans leading-relaxed mb-6">
                {college.description}
              </p>
            </div>

            {/* Quick Stats */}
            <div className="lg:col-span-1">
              <Card className="border-2 border-border">
                <CardHeader>
                  <CardTitle className="text-xl font-space-grotesk">Quick Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold font-space-grotesk text-primary">
                        {college.total_students ? `${Math.round(college.total_students / 1000)}K` : "N/A"}
                      </div>
                      <p className="text-sm text-muted-foreground font-dm-sans">Students</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold font-space-grotesk text-secondary">
                        {college.total_faculty || "N/A"}
                      </div>
                      <p className="text-sm text-muted-foreground font-dm-sans">Faculty</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}